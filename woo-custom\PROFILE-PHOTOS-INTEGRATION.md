# WooCommerce Profil Fotoğrafı Entegrasyonu

Bu doküman<PERSON>yon, <PERSON><PERSON>'deki profil ve kapak fotoğrafı düzenleme özelliğinin WooCommerce hesap sayfasına nasıl entegre edildiğini açıklar.

## 📋 Genel Bakış

WooCommerce "Hesap Detayları" sayfasına Tutor LMS tarzında profil ve kapak fotoğrafı yükleme/düzenleme özelliği eklendi. Kullanıcılar artık WooCommerce hesap sayfasından profil fotoğraflarını yönetebilir.

## 🏗️ <PERSON><PERSON><PERSON> Yapı

### Ana Sınıf: `WooCustom_Profile_Photos`
**Dosya:** `includes/class-woo-custom-profile-photos.php`

Bu sınıf tüm profil fotoğrafı işlemlerini yönetir:
- Singleton pattern kullanır
- WordPress hook'larını yönetir
- AJAX işlemlerini handle eder
- Güvenlik kontrollerini yapar

### Hook Entegrasyonu
```php
add_action('woocommerce_edit_account_form_fields', array($this, 'add_profile_photos_section'));
```
Bu hook sayesinde WooCommerce hesap detayları formuna profil fotoğrafı bölümü eklenir.

## 🔧 Teknik Detaylar

### Meta Keys (Tutor LMS Uyumlu)
- **Profil Fotoğrafı:** `_tutor_profile_photo`
- **Kapak Fotoğrafı:** `_tutor_cover_photo`

Bu meta key'ler Tutor LMS ile aynı olduğu için tam uyumluluk sağlanır.

### AJAX Endpoints
1. **Fotoğraf Yükleme:** `woo_custom_user_photo_upload`
2. **Fotoğraf Silme:** `woo_custom_user_photo_remove`

### Güvenlik Önlemleri
- WordPress nonce kontrolü
- Dosya tipi validasyonu (sadece resim dosyaları)
- Dosya boyutu kontrolü (WordPress max upload size)
- Kullanıcı oturum kontrolü

## 📁 Dosya Yapısı

```
woo-custom/
├── includes/
│   └── class-woo-custom-profile-photos.php    # Ana sınıf
├── templates/
│   └── myaccount/
│       └── profile-photos.php                 # Template dosyası
├── assets/
│   ├── css/
│   │   └── profile-photos.css                 # Stiller
│   ├── js/
│   │   └── profile-photos.js                  # JavaScript
│   └── images/
│       ├── profile-placeholder.svg            # Profil placeholder
│       └── cover-placeholder.svg              # Kapak placeholder
└── languages/
    └── woo-custom-tr_TR.po                    # Türkçe çeviriler
```

## 🎨 Frontend Yapısı

### HTML Yapısı
```html
<div class="woo-custom-profile-photos-section">
    <h3>Profil ve Kapak Fotoğrafı</h3>
    <div id="woo_custom_profile_cover_photo_editor">
        <!-- Kapak fotoğrafı alanı -->
        <div id="woo_custom_cover_area">...</div>

        <!-- Bilgi alanı -->
        <div id="woo_custom_photo_meta_area">...</div>

        <!-- Profil fotoğrafı alanı -->
        <div id="woo_custom_profile_area">...</div>

        <!-- Profil fotoğrafı seçenekleri -->
        <div id="woo_custom_pp_option">...</div>
    </div>
</div>
```

### CSS Sınıfları
- `.woo-custom-profile-photos-section` - Ana container
- `.woo-custom-cover-area` - Kapak fotoğrafı alanı
- `.woo-custom-profile-area` - Profil fotoğrafı alanı
- `.woo-custom-pp-options` - Profil fotoğrafı seçenekleri

## ⚡ JavaScript Fonksiyonları

### Ana Obje: `WooCustomProfilePhotos`
```javascript
WooCustomProfilePhotos = {
    init: function() { ... },
    bindEvents: function() { ... },
    uploadPhoto: function(file) { ... },
    deletePhoto: function(photoType) { ... },
    updatePhotoDisplay: function(photoUrl) { ... },
    showMessage: function(message, type) { ... }
}
```

### Event Handlers
- Kapak fotoğrafı yükleme butonu
- Profil fotoğrafı alanı tıklama
- Fotoğraf silme butonları
- Dosya seçimi

## 🔄 AJAX İş Akışı

### Fotoğraf Yükleme
1. Kullanıcı dosya seçer
2. JavaScript dosya validasyonu yapar
3. FormData ile AJAX request gönderilir
4. PHP'de güvenlik kontrolleri yapılır
5. WordPress media library'ye eklenir
6. User meta güncellenir
7. Frontend'de fotoğraf güncellenir

### Fotoğraf Silme
1. Kullanıcı silme butonuna tıklar
2. Onay dialogu gösterilir
3. AJAX request gönderilir
4. PHP'de fotoğraf ve meta silinir
5. Frontend'de placeholder gösterilir

## 🌐 Çoklu Dil Desteği

Tüm metinler `__()` fonksiyonu ile çevrilebilir hale getirildi:
```php
__('Profil ve Kapak Fotoğrafı', 'woo-custom')
__('Fotoğraf başarıyla yüklendi.', 'woo-custom')
```

## 📱 Responsive Tasarım

CSS media queries ile mobil uyumluluk sağlandı:
```css
@media (max-width: 768px) {
    .woo-custom-cover-area { height: 150px; }
    .woo-custom-profile-area {
        width: 100px;
        height: 100px;
    }
}
```

## 🔌 Plugin Entegrasyonu

Ana plugin dosyasında (`woo-custom.php`) sınıf dahil edildi:
```php
require_once WOO_CUSTOM_PLUGIN_DIR . 'includes/class-woo-custom-profile-photos.php';
WooCustom_Profile_Photos::instance();
```

## 🎯 Kullanım Senaryoları

1. **İlk Kurulum:** Placeholder fotoğraflar gösterilir
2. **Fotoğraf Yükleme:** Drag & drop veya click ile dosya seçimi
3. **Fotoğraf Güncelleme:** Mevcut fotoğraf üzerine yeni fotoğraf yükleme
4. **Fotoğraf Silme:** X butonu ile silme ve placeholder'a dönüş

## 🛡️ Güvenlik Özellikleri

- **Nonce Kontrolü:** Her AJAX request'te güvenlik token kontrolü
- **Dosya Tipi:** Sadece image/* MIME type'ları kabul edilir
- **Dosya Boyutu:** WordPress max upload size limiti
- **Kullanıcı Kontrolü:** Sadece giriş yapmış kullanıcılar
- **Sanitization:** Tüm input'lar sanitize edilir

## 🔧 Özelleştirme Noktaları

### CSS Özelleştirme
`assets/css/profile-photos.css` dosyasından stiller değiştirilebilir.

### JavaScript Özelleştirme
`assets/js/profile-photos.js` dosyasından davranışlar değiştirilebilir.

### Template Özelleştirme
`templates/myaccount/profile-photos.php` dosyasından HTML yapısı değiştirilebilir.

## 📊 Performans Optimizasyonları

- **Lazy Loading:** Sadece account sayfasında CSS/JS yüklenir
- **AJAX:** Sayfa yenilenmeden işlemler yapılır
- **Image Optimization:** WordPress'in built-in image processing'i kullanılır
- **Caching:** WordPress object cache ile uyumlu

## 🐛 Hata Yönetimi

- JavaScript'te try-catch blokları
- PHP'de wp_send_json_error() kullanımı
- Kullanıcı dostu hata mesajları
- Console'da detaylı hata logları

## 🚀 Sonuç

Bu entegrasyon sayesinde WooCommerce kullanıcıları artık hesap sayfalarından kolayca profil fotoğraflarını yönetebilir ve Tutor LMS ile tam uyumluluk sağlanır. Sistem güvenli, performanslı ve kullanıcı dostu bir şekilde tasarlanmıştır.