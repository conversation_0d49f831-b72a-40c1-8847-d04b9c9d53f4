<?php
/**
 * My Account - Wishlist
 *
 * @package WooCustom
 */

defined('ABSPATH') || exit;

$wishlist = WooCustom_Wishlist::instance();
$wishlist_products = $wishlist->get_wishlist_products();
$wishlist_count = $wishlist->get_wishlist_count();
?>

<div class="woo-custom-wishlist">
    <div class="wishlist-header">
        <h2>
            <?php esc_html_e('İstek Listem', 'woo-custom'); ?>
            <span class="wishlist-count-badge">
                <?php echo esc_html($wishlist_count); ?>
            </span>
        </h2>

        <?php if (!empty($wishlist_products)) : ?>
            <div class="wishlist-view-toggle">
                <button class="view-toggle-btn active" data-view="list" title="Liste Görünümü">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
                    </svg>
                </button>
                <button class="view-toggle-btn" data-view="grid" title="Grid Görünümü">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 3v8h8V3H3zm6 6H5V5h4v4zm-6 4v8h8v-8H3zm6 6H5v-4h4v4zm4-16v8h8V3h-8zm6 6h-4V5h4v4zm-6 4v8h8v-8h-8zm6 6h-4v-4h4v4z"/>
                    </svg>
                </button>
            </div>
        <?php endif; ?>
    </div>

    <?php if (!empty($wishlist_products)) : ?>
        <div class="wishlist-products">
            <!-- Liste Görünümü -->
            <div class="wishlist-table-wrapper wishlist-view-list">
                <table class="shop_table shop_table_responsive wishlist_table">
                    <thead>
                        <tr>
                            <th class="product-remove">&nbsp;</th>
                            <th class="product-thumbnail">&nbsp;</th>
                            <th class="product-name"><?php esc_html_e('Ürün', 'woo-custom'); ?></th>
                            <th class="product-price"><?php esc_html_e('Fiyat', 'woo-custom'); ?></th>
                            <th class="product-stock-status"><?php esc_html_e('Stok Durumu', 'woo-custom'); ?></th>
                            <th class="product-add-to-cart">&nbsp;</th>
                            <th class="product-date-added"><?php esc_html_e('Eklenme Tarihi', 'woo-custom'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($wishlist_products as $item) : 
                            $product = $item['product'];
                            $date_added = $item['date_added'];
                        ?>
                            <tr class="wishlist-item" data-product-id="<?php echo esc_attr($product->get_id()); ?>">
                                <td class="product-remove">
                                    <button class="woo-custom-wishlist-btn in-wishlist remove-from-wishlist" 
                                            data-product-id="<?php echo esc_attr($product->get_id()); ?>"
                                            title="<?php esc_attr_e('Remove from wishlist', 'woo-custom'); ?>">
                                        <span class="remove-icon">×</span>
                                    </button>
                                </td>
                                
                                <td class="product-thumbnail">
                                    <a href="<?php echo esc_url($product->get_permalink()); ?>">
                                        <?php echo $product->get_image('woocommerce_thumbnail'); ?>
                                    </a>
                                </td>
                                
                                <td class="product-name" data-title="<?php esc_attr_e('Product', 'woo-custom'); ?>">
                                    <a href="<?php echo esc_url($product->get_permalink()); ?>">
                                        <?php echo esc_html($product->get_name()); ?>
                                    </a>
                                </td>
                                
                                <td class="product-price" data-title="<?php esc_attr_e('Price', 'woo-custom'); ?>">
                                    <?php echo $product->get_price_html(); ?>
                                </td>
                                
                                <td class="product-stock-status" data-title="<?php esc_attr_e('Stok Durumu', 'woo-custom'); ?>">
                                    <?php
                                    if ($product->is_in_stock()) {
                                        echo '<span class="in-stock">' . esc_html__('Stokta var', 'woo-custom') . '</span>';
                                    } else {
                                        echo '<span class="out-of-stock">' . esc_html__('Stokta yok', 'woo-custom') . '</span>';
                                    }
                                    ?>
                                </td>
                                
                                <td class="product-add-to-cart" data-title="<?php esc_attr_e('Add to Cart', 'woo-custom'); ?>">
                                    <?php if ($product->is_purchasable() && $product->is_in_stock()) : ?>
                                        <?php
                                        woocommerce_template_loop_add_to_cart(array(
                                            'product' => $product
                                        ));
                                        ?>
                                    <?php else : ?>
                                        <span class="not-available"><?php esc_html_e('Not available', 'woo-custom'); ?></span>
                                    <?php endif; ?>
                                </td>
                                
                                <td class="product-date-added" data-title="<?php esc_attr_e('Date Added', 'woo-custom'); ?>">
                                    <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($date_added))); ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Grid Görünümü -->
            <div class="wishlist-grid-wrapper wishlist-view-grid" style="display: none;">
                <div class="wishlist-grid">
                    <?php foreach ($wishlist_products as $item) :
                        $product = $item['product'];
                        $date_added = $item['date_added'];

                        if (!$product || !$product->exists()) {
                            continue;
                        }
                    ?>
                        <div class="wishlist-grid-item">
                            <div class="grid-item-image">
                                <a href="<?php echo esc_url($product->get_permalink()); ?>">
                                    <?php echo $product->get_image('woocommerce_gallery_thumbnail'); ?>
                                </a>
                                <button class="remove-from-wishlist grid-remove"
                                        data-product-id="<?php echo esc_attr($product->get_id()); ?>"
                                        title="<?php esc_attr_e('İstek listesinden çıkar', 'woo-custom'); ?>">
                                    ×
                                </button>
                            </div>

                            <div class="grid-item-content">
                                <h4 class="grid-item-title">
                                    <a href="<?php echo esc_url($product->get_permalink()); ?>">
                                        <?php echo esc_html($product->get_name()); ?>
                                    </a>
                                </h4>

                                <div class="grid-item-price">
                                    <?php echo $product->get_price_html(); ?>
                                </div>

                                <div class="grid-item-stock">
                                    <?php
                                    if ($product->is_in_stock()) {
                                        echo '<span class="in-stock">' . esc_html__('Stokta var', 'woo-custom') . '</span>';
                                    } else {
                                        echo '<span class="out-of-stock">' . esc_html__('Stokta yok', 'woo-custom') . '</span>';
                                    }
                                    ?>
                                </div>

                                <div class="grid-item-actions">
                                    <?php if ($product->is_purchasable() && $product->is_in_stock()) : ?>
                                        <a href="<?php echo esc_url($product->add_to_cart_url()); ?>"
                                           class="button add_to_cart_button"
                                           data-product_id="<?php echo esc_attr($product->get_id()); ?>">
                                            <?php esc_html_e('Sepete Ekle', 'woo-custom'); ?>
                                        </a>
                                    <?php endif; ?>
                                </div>

                                <div class="grid-item-date">
                                    <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($date_added))); ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    <?php else : ?>
        <div class="wishlist-empty">
            <p><?php esc_html_e('İstek listeniz şu anda boş.', 'woo-custom'); ?></p>
            <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" class="button">
                <?php esc_html_e('Alışverişe Başla', 'woo-custom'); ?>
            </a>
        </div>
    <?php endif; ?>
</div>

<style>
.woo-custom-wishlist .wishlist-header {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.woo-custom-wishlist .wishlist-header h2 {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 0;
}

.wishlist-view-toggle {
    display: flex;
    gap: 5px;
    background: #f8f9fa;
    padding: 4px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.view-toggle-btn {
    background: transparent;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-toggle-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.view-toggle-btn.active {
    background: #007cba;
    color: white;
    box-shadow: 0 2px 4px rgba(0,124,186,0.3);
}

.woo-custom-wishlist .wishlist-count-badge {
    background: #ffab2c;
    color: white;
    font-size: 14px;
    font-weight: 500;
    padding: 4px 12px;
    border-radius: 20px;
    min-width: 24px;
    text-align: center;
    line-height: 1.2;
}

.wishlist-table-wrapper {
    overflow-x: auto;
}

.wishlist_table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e1e5e9;
}

.wishlist_table th,
.wishlist_table td {
    padding: 16px;
    text-align: left;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.wishlist_table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    color: #495057;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #dee2e6;
}

.wishlist_table tbody tr {
    transition: all 0.2s ease;
}

.wishlist_table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.product-remove {
    width: 50px;
    text-align: center;
}

.product-thumbnail {
    width: 80px;
}

.product-thumbnail img {
    max-width: 70px;
    height: 70px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    transition: all 0.3s ease;
}

.product-thumbnail img:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.product-name a {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    font-size: 15px;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.product-name a:hover {
    color: #007cba;
}

.product-price {
    font-weight: 600;
    font-size: 16px;
    color: #2c5aa0;
}

.remove-from-wishlist {
    background: #f8f8f8;
    color: #000000;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
}

.remove-from-wishlist:hover {
    background: #e0e0e0;
    color: #333333;
}

.in-stock {
    background: #d4edda;
    color: #155724;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid #c3e6cb;
}

.out-of-stock {
    background: #f8d7da;
    color: #721c24;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid #f5c6cb;
}

/* Grid Görünümü */
.wishlist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.wishlist-grid-item {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.wishlist-grid-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #007cba;
}

.grid-item-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
}

.grid-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.grid-item-image:hover img {
    transform: scale(1.05);
}

.grid-remove {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(248, 248, 248, 0.95);
    color: #000;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    cursor: pointer;
    font-size: 18px;
    line-height: 1;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.grid-remove:hover {
    background: #e0e0e0;
    transform: scale(1.1);
}

.grid-item-content {
    padding: 16px;
}

.grid-item-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    line-height: 1.4;
}

.grid-item-title a {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.grid-item-title a:hover {
    color: #007cba;
}

.grid-item-price {
    font-size: 18px;
    font-weight: 600;
    color: #2c5aa0;
    margin-bottom: 8px;
}

.grid-item-stock {
    margin-bottom: 12px;
}

.grid-item-actions {
    margin-bottom: 8px;
}

.grid-item-actions .button {
    width: 100%;
    text-align: center;
    padding: 10px;
    background: #007cba;
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.grid-item-actions .button:hover {
    background: #005a87;
    transform: translateY(-1px);
}

.grid-item-date {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
    padding-top: 8px;
    border-top: 1px solid #f1f3f4;
}

.wishlist-empty {
    text-align: center;
    padding: 40px 20px;
}

.wishlist-empty .button {
    margin-top: 20px;
}

@media (max-width: 768px) {
    .wishlist_table,
    .wishlist_table thead,
    .wishlist_table tbody,
    .wishlist_table th,
    .wishlist_table td,
    .wishlist_table tr {
        display: block;
    }
    
    .wishlist_table thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }
    
    .wishlist_table tr {
        border: 1px solid #ccc;
        margin-bottom: 10px;
        padding: 10px;
    }
    
    .wishlist_table td {
        border: none;
        position: relative;
        padding-left: 50%;
    }
    
    .wishlist_table td:before {
        content: attr(data-title) ": ";
        position: absolute;
        left: 6px;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        font-weight: bold;
    }

    .wishlist-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .wishlist-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }

    .grid-item-content {
        padding: 12px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const viewToggleBtns = document.querySelectorAll('.view-toggle-btn');
    const listView = document.querySelector('.wishlist-view-list');
    const gridView = document.querySelector('.wishlist-view-grid');

    // Varsayılan görünümü localStorage'dan al veya 'list' kullan
    const currentView = localStorage.getItem('wishlist-view') || 'list';
    switchView(currentView);

    viewToggleBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const view = this.getAttribute('data-view');
            switchView(view);
            localStorage.setItem('wishlist-view', view);
        });
    });

    function switchView(view) {
        // Buton durumlarını güncelle
        viewToggleBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-view') === view) {
                btn.classList.add('active');
            }
        });

        // Görünümleri değiştir
        if (view === 'grid') {
            if (listView) listView.style.display = 'none';
            if (gridView) gridView.style.display = 'block';
        } else {
            if (listView) listView.style.display = 'block';
            if (gridView) gridView.style.display = 'none';
        }
    }
});
</script>
