<?php
/**
 * WooCommerce My Account customizations
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WooCustom_Account {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Add custom menu items to My Account
        add_filter('woocommerce_account_menu_items', array($this, 'add_account_menu_items'));
        
        // Add custom endpoints
        add_action('init', array($this, 'add_endpoints'));
        
        // Add query vars
        add_filter('query_vars', array($this, 'add_query_vars'));
        
        // Handle endpoint content
        add_action('woocommerce_account_wishlist_endpoint', array($this, 'wishlist_endpoint_content'));
        add_action('woocommerce_account_my-reviews_endpoint', array($this, 'my_reviews_endpoint_content'));
        
        // Set page titles
        add_filter('woocommerce_endpoint_wishlist_title', array($this, 'wishlist_endpoint_title'));
        add_filter('woocommerce_endpoint_my-reviews_title', array($this, 'my_reviews_endpoint_title'));
    }
    
    /**
     * Add custom menu items to My Account
     */
    public function add_account_menu_items($items) {
        // Insert wishlist after orders
        $new_items = array();
        foreach ($items as $key => $item) {
            $new_items[$key] = $item;
            if ($key === 'orders') {
                $new_items['wishlist'] = __('İstek Listesi', 'woo-custom'); // Turkish: Wishlist
            }
        }

        // Insert my-reviews before edit-account
        $final_items = array();
        foreach ($new_items as $key => $item) {
            if ($key === 'edit-account') {
                $final_items['my-reviews'] = __('Değerlendirmelerim', 'woo-custom'); // Turkish: My Reviews
            }
            $final_items[$key] = $item;
        }
        
        return $final_items;
    }
    
    /**
     * Add custom endpoints
     */
    public function add_endpoints() {
        add_rewrite_endpoint('wishlist', EP_ROOT | EP_PAGES);
        add_rewrite_endpoint('my-reviews', EP_ROOT | EP_PAGES);
    }
    
    /**
     * Add query vars
     */
    public function add_query_vars($vars) {
        $vars[] = 'wishlist';
        $vars[] = 'my-reviews';
        return $vars;
    }
    
    /**
     * Wishlist endpoint content
     */
    public function wishlist_endpoint_content() {
        wc_get_template('myaccount/wishlist.php', array(), '', WOO_CUSTOM_PLUGIN_DIR . 'templates/');
    }
    
    /**
     * My Reviews endpoint content
     */
    public function my_reviews_endpoint_content() {
        wc_get_template('myaccount/my-reviews.php', array(), '', WOO_CUSTOM_PLUGIN_DIR . 'templates/');
    }
    
    /**
     * Wishlist endpoint title
     */
    public function wishlist_endpoint_title($title) {
        return __('İstek Listesi', 'woo-custom');
    }

    /**
     * My Reviews endpoint title
     */
    public function my_reviews_endpoint_title($title) {
        return __('Değerlendirmelerim', 'woo-custom');
    }
}
