/**
 * WooCommerce Profile Photos CSS
 * Based on <PERSON><PERSON> profile photo editor styles
 */

.woo-custom-profile-photos-section {
    margin: 20px 0;
    padding: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.woo-custom-profile-photos-section h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: #333;
}

#woo_custom_profile_cover_photo_editor {
    position: relative;
    margin-bottom: 20px;
}

/* Cover Photo Area */
.woo-custom-cover-area {
    position: relative;
    width: 100%;
    height: 200px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 15px;
    border: 2px solid #e1e5e9;
}

.woo-custom-cover-area .woo_custom_overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.woo-custom-cover-area:hover .woo_custom_overlay {
    opacity: 1;
}

.woo_custom_cover_deleter {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    transition: background 0.3s ease;
}

.woo_custom_cover_deleter:hover {
    background: #ff4757;
    color: white;
}

.woo-custom-delete-icon {
    font-size: 18px;
    font-weight: bold;
}

/* Profile Photo Area */
.woo-custom-profile-area {
    position: relative;
    width: 120px;
    height: 120px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 50%;
    border: 4px solid #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: -60px auto 20px auto;
    cursor: pointer;
    overflow: hidden;
}

.woo-custom-profile-area .woo_custom_overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50%;
}

.woo-custom-profile-area:hover .woo_custom_overlay {
    opacity: 1;
}

.woo-custom-icon-camera {
    color: white;
    font-size: 24px;
}

.woo-custom-icon-camera:before {
    content: "📷";
}

/* Profile Photo Options */
.woo-custom-pp-options {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 10;
    min-width: 150px;
}

.woo-custom-up-arrow {
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
}

.woo-custom-profile-uploader {
    display: block;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 3px;
    transition: background 0.3s ease;
    text-decoration: none;
    color: #333;
}

.woo-custom-profile-uploader:hover {
    background: #f5f5f5;
}

.woo-custom-profile-uploader i {
    margin-right: 8px;
}

.woo-custom-icon-upload:before {
    content: "📁";
}

.woo-custom-icon-delete:before {
    content: "🗑️";
}

/* Photo Meta Area */
.woo-custom-photo-meta {
    text-align: center;
    margin: 15px 0;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
}

.woo-custom-photo-info {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
    font-size: 12px;
    color: #666;
}

.woo-custom-loader-area {
    display: none;
    color: #007cba;
    font-weight: bold;
    margin-top: 10px;
}

/* Buttons */
.woo-custom-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.woo-custom-btn-primary {
    background: #007cba;
    color: white;
}

.woo-custom-btn-primary:hover {
    background: #005a87;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .woo-custom-cover-area {
        height: 150px;
    }

    .woo-custom-profile-area {
        width: 100px;
        height: 100px;
        margin: -50px auto 20px auto;
    }

    .woo-custom-photo-info {
        flex-direction: column;
        gap: 5px;
    }

    .woo-custom-profile-photos-section {
        padding: 15px;
    }
}

/* Loading States */
.woo-custom-uploading .woo-custom-loader-area {
    display: block;
}

.woo-custom-uploading .woo_custom_overlay {
    opacity: 1;
    background: rgba(0, 0, 0, 0.7);
}

.woo-custom-uploading .woo_custom_cover_uploader,
.woo-custom-uploading .woo_custom_pp_uploader {
    pointer-events: none;
    opacity: 0.6;
}

/* Error States */
.woo-custom-error {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

/* Success States */
.woo-custom-success {
    color: #155724;
    background: #d4edda;
    border: 1px solid #c3e6cb;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}