# Woo Custom Plugin - Installation Guide

## Prerequisites

Before installing the Woo Custom plugin, ensure you have:

1. **WordPress 6.0 or higher**
2. **WooCommerce 8.0 or higher** (installed and activated)
3. **PHP 7.4 or higher**
4. **MySQL 5.6 or higher**

## Installation Steps

### Step 1: Upload Plugin Files

1. Download or copy the `woo-custom` folder
2. Upload it to your WordPress installation directory: `/wp-content/plugins/`
3. The final path should be: `/wp-content/plugins/woo-custom/`

### Step 2: Activate the Plugin

1. Log in to your WordPress admin dashboard
2. Navigate to **Plugins > Installed Plugins**
3. Find "Woo Custom" in the plugin list
4. Click **Activate**

### Step 3: Verify Installation

After activation, the plugin will automatically:

1. **Create database table**: `wp_woo_custom_wishlist`
2. **Register endpoints**: `/my-account/wishlist/` and `/my-account/my-reviews/`
3. **Add menu items** to WooCommerce My Account page

### Step 4: Refresh Permalinks

1. Go to **Settings > Permalinks**
2. Click **Save Changes** (no need to change anything)
3. This ensures the new endpoints work correctly

### Step 5: Test Functionality

1. **Visit your shop page** - you should see heart icons on products when hovering (for logged-in users)
2. **Go to My Account** - you should see "İstek Listesi" and "Değerlendirmelerim" menu items
3. **Click on wishlist items** - they should add/remove products with visual feedback

## Verification

To verify the plugin is working correctly:

### Check Database Table

Run this SQL query in your database:
```sql
SHOW TABLES LIKE 'wp_woo_custom_wishlist';
```

You should see the table exists with these columns:
- `id` (bigint, primary key)
- `user_id` (bigint)
- `product_id` (bigint)
- `date_added` (datetime)

### Check My Account Page

1. Log in as a customer
2. Go to **My Account**
3. Verify these menu items exist:
   - İstek Listesi (Wishlist)
   - Değerlendirmelerim (My Reviews)

### Check Product Pages

1. Visit shop or category pages
2. Hover over products (while logged in)
3. You should see heart icons appear
4. Click heart icons to add/remove from wishlist

## Troubleshooting

### Heart Icons Not Appearing

**Problem**: Heart icons don't show on product hover

**Solutions**:
1. Ensure you're logged in
2. Check if JavaScript is enabled
3. Clear browser cache
4. Check for JavaScript errors in browser console

### My Account Pages Show 404

**Problem**: Wishlist or My Reviews pages show "Page not found"

**Solutions**:
1. Go to **Settings > Permalinks** and click **Save Changes**
2. Deactivate and reactivate the plugin
3. Check if WooCommerce is active

### Database Table Not Created

**Problem**: Wishlist functionality doesn't work

**Solutions**:
1. Deactivate and reactivate the plugin
2. Check database user permissions
3. Manually run the table creation SQL:

```sql
CREATE TABLE wp_woo_custom_wishlist (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    user_id bigint(20) NOT NULL,
    product_id bigint(20) NOT NULL,
    date_added datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_user_product (user_id, product_id),
    KEY user_id (user_id),
    KEY product_id (product_id)
);
```

### AJAX Errors

**Problem**: Adding/removing from wishlist doesn't work

**Solutions**:
1. Check if user is logged in
2. Verify nonce security tokens
3. Check browser console for JavaScript errors
4. Ensure AJAX URL is correct

## File Permissions

Ensure these files have proper permissions:

```
woo-custom/                     (755)
├── woo-custom.php             (644)
├── includes/                  (755)
│   ├── *.php                  (644)
├── templates/                 (755)
│   └── myaccount/            (755)
│       ├── *.php             (644)
├── assets/                    (755)
│   ├── css/                   (755)
│   │   └── *.css             (644)
│   └── js/                    (755)
│       └── *.js              (644)
```

## Testing

After installation, you can run basic tests by visiting:
```
yoursite.com/wp-admin/admin.php?page=woo-custom-tests
```

Or add this to your URL to run tests:
```
?run_woo_custom_tests=1
```

## Support

If you encounter issues:

1. **Check WordPress debug log** (`/wp-content/debug.log`)
2. **Verify WooCommerce compatibility**
3. **Test with default theme** to rule out theme conflicts
4. **Disable other plugins** to check for conflicts

## Uninstallation

To completely remove the plugin:

1. **Deactivate** the plugin
2. **Delete** the plugin files
3. **Remove database table** (optional):
   ```sql
   DROP TABLE wp_woo_custom_wishlist;
   ```

## Next Steps

After successful installation:

1. **Customize styling** by editing CSS files or adding custom CSS
2. **Translate text** by creating language files
3. **Test with your theme** to ensure compatibility
4. **Train users** on the new wishlist and reviews features

The plugin is now ready to use!
