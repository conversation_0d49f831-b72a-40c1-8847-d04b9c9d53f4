/**
 * WooCommerce Profile Photos JavaScript
 * Based on Tutor LMS profile photo editor functionality
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        WooCustomProfilePhotos.init();
    });

    // Main Profile Photos object
    window.WooCustomProfilePhotos = {

        // Initialize the functionality
        init: function() {
            this.bindEvents();
        },

        // Bind event handlers
        bindEvents: function() {
            // Cover photo upload button
            $(document).on('click', '.woo_custom_cover_uploader', this.handleCoverUploadClick);

            // Profile photo area click
            $(document).on('click', '#woo_custom_profile_area', this.handleProfileAreaClick);

            // Profile photo upload button
            $(document).on('click', '.woo_custom_pp_uploader', this.handleProfileUploadClick);

            // Profile photo delete button
            $(document).on('click', '.woo_custom_pp_deleter', this.handleProfileDeleteClick);

            // Cover photo delete button
            $(document).on('click', '.woo_custom_cover_deleter', this.handleCoverDeleteClick);

            // File input change
            $(document).on('change', '#woo_custom_photo_dialogue_box', this.handleFileSelect);

            // Hide profile options when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#woo_custom_profile_area, #woo_custom_pp_option').length) {
                    $('#woo_custom_pp_option').hide();
                }
            });
        },

        // Handle cover upload button click
        handleCoverUploadClick: function(e) {
            e.preventDefault();
            WooCustomProfilePhotos.currentPhotoType = 'cover_photo';
            $('#woo_custom_photo_dialogue_box').click();
        },

        // Handle profile area click
        handleProfileAreaClick: function(e) {
            e.preventDefault();
            $('#woo_custom_pp_option').toggle();
        },

        // Handle profile upload button click
        handleProfileUploadClick: function(e) {
            e.preventDefault();
            WooCustomProfilePhotos.currentPhotoType = 'profile_photo';
            $('#woo_custom_photo_dialogue_box').click();
            $('#woo_custom_pp_option').hide();
        },

        // Handle profile delete button click
        handleProfileDeleteClick: function(e) {
            e.preventDefault();

            if (!confirm(woo_custom_profile_photos.i18n.delete_confirm)) {
                return;
            }

            WooCustomProfilePhotos.deletePhoto('profile_photo');
            $('#woo_custom_pp_option').hide();
        },

        // Handle cover delete button click
        handleCoverDeleteClick: function(e) {
            e.preventDefault();

            if (!confirm(woo_custom_profile_photos.i18n.delete_confirm)) {
                return;
            }

            WooCustomProfilePhotos.deletePhoto('cover_photo');
        },

        // Handle file selection
        handleFileSelect: function(e) {
            var file = e.target.files[0];
            if (!file) {
                return;
            }

            // Validate file type
            if (!file.type.match('image.*')) {
                WooCustomProfilePhotos.showMessage(woo_custom_profile_photos.i18n.upload_error + ': Sadece resim dosyaları yüklenebilir.', 'error');
                return;
            }

            // Validate file size (get from hidden input)
            var maxSize = $('.upload_max_filesize').val();
            if (file.size > maxSize) {
                WooCustomProfilePhotos.showMessage(woo_custom_profile_photos.i18n.upload_error + ': Dosya boyutu çok büyük.', 'error');
                return;
            }

            WooCustomProfilePhotos.uploadPhoto(file);
        },

        // Upload photo via AJAX
        uploadPhoto: function(file) {
            var formData = new FormData();
            formData.append('action', 'woo_custom_user_photo_upload');
            formData.append('photo_file', file);
            formData.append('photo_type', this.currentPhotoType);
            formData.append('nonce', woo_custom_profile_photos.nonce);

            // Show loading state
            this.showLoading(true);

            $.ajax({
                url: woo_custom_profile_photos.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    WooCustomProfilePhotos.handleUploadResponse(response);
                },
                error: function(xhr, status, error) {
                    WooCustomProfilePhotos.showMessage(woo_custom_profile_photos.i18n.upload_error, 'error');
                },
                complete: function() {
                    WooCustomProfilePhotos.showLoading(false);
                    // Clear file input
                    $('#woo_custom_photo_dialogue_box').val('');
                }
            });
        },

        // Handle upload response
        handleUploadResponse: function(response) {
            if (response.success) {
                this.showMessage(response.data.message, 'success');
                this.updatePhotoDisplay(response.data.photo_url);
            } else {
                this.showMessage(response.data.message || woo_custom_profile_photos.i18n.upload_error, 'error');
            }
        },

        // Update photo display
        updatePhotoDisplay: function(photoUrl) {
            if (this.currentPhotoType === 'cover_photo') {
                $('#woo_custom_cover_area').css('background-image', 'url(' + photoUrl + ')');
                $('.woo_custom_cover_deleter').show();
            } else {
                $('#woo_custom_profile_area').css('background-image', 'url(' + photoUrl + ')');
                $('.woo_custom_pp_deleter').show();
            }
        },

        // Delete photo via AJAX
        deletePhoto: function(photoType) {
            // Show loading state
            this.showLoading(true);

            $.ajax({
                url: woo_custom_profile_photos.ajax_url,
                type: 'POST',
                data: {
                    action: 'woo_custom_user_photo_remove',
                    photo_type: photoType,
                    nonce: woo_custom_profile_photos.nonce
                },
                success: function(response) {
                    WooCustomProfilePhotos.handleDeleteResponse(response, photoType);
                },
                error: function(xhr, status, error) {
                    WooCustomProfilePhotos.showMessage(woo_custom_profile_photos.i18n.delete_error, 'error');
                },
                complete: function() {
                    WooCustomProfilePhotos.showLoading(false);
                }
            });
        },

        // Handle delete response
        handleDeleteResponse: function(response, photoType) {
            if (response.success) {
                this.showMessage(response.data.message, 'success');
                this.resetPhotoDisplay(photoType);
            } else {
                this.showMessage(response.data.message || woo_custom_profile_photos.i18n.delete_error, 'error');
            }
        },

        // Reset photo display to placeholder
        resetPhotoDisplay: function(photoType) {
            if (photoType === 'cover_photo') {
                var placeholder = $('#woo_custom_cover_area').data('fallback');
                $('#woo_custom_cover_area').css('background-image', 'url(' + placeholder + ')');
                $('.woo_custom_cover_deleter').hide();
            } else {
                var placeholder = $('#woo_custom_profile_area').data('fallback');
                $('#woo_custom_profile_area').css('background-image', 'url(' + placeholder + ')');
                $('.woo_custom_pp_deleter').hide();
            }
        },

        // Show loading state
        showLoading: function(show) {
            if (show) {
                $('#woo_custom_profile_cover_photo_editor').addClass('woo-custom-uploading');
                $('.woo-custom-loader-area').show();
            } else {
                $('#woo_custom_profile_cover_photo_editor').removeClass('woo-custom-uploading');
                $('.woo-custom-loader-area').hide();
            }
        },

        // Show message to user
        showMessage: function(message, type) {
            // Remove existing messages
            $('.woo-custom-message').remove();

            // Create message element
            var messageClass = type === 'error' ? 'woo-custom-error' : 'woo-custom-success';
            var messageHtml = '<div class="woo-custom-message ' + messageClass + '">' + message + '</div>';

            // Insert message
            $('#woo_custom_profile_cover_photo_editor').before(messageHtml);

            // Auto-hide after 5 seconds
            setTimeout(function() {
                $('.woo-custom-message').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }
    };

})(jQuery);